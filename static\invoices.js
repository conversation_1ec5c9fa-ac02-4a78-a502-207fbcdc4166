// إدارة الفواتير
Object.assign(AccountingApp.prototype, {
    // صفحة إدارة الفواتير
    getInvoicesPage() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h1>إدارة الفواتير الإلكترونية</h1>
                    <button class="btn btn-primary" onclick="app.showInvoiceForm()">
                        ➕ فاتورة جديدة
                    </button>
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>تاريخ الفاتورة</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                                <th>حالة ZATCA</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="invoices-table-body">
                            <tr><td colspan="7">جاري التحميل...</td></tr>
                        </tbody>
                    </table>
                </div>
                
                <div id="invoice-modal" class="modal" style="display: none;">
                    <div class="modal-content" style="max-width: 800px;">
                        <div class="modal-header">
                            <h2 id="invoice-modal-title">فاتورة جديدة</h2>
                            <span class="close" onclick="app.hideInvoiceForm()">&times;</span>
                        </div>
                        <form data-form="invoice" id="invoice-form">
                            <input type="hidden" id="invoice-id" name="id">
                            <input type="hidden" id="invoice-company-id" name="company_id" value="${this.currentUser?.company_id || ''}">
                            
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                                <div class="form-group">
                                    <label for="invoice-number">رقم الفاتورة *</label>
                                    <input type="text" id="invoice-number" name="invoice_number" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="invoice-customer">العميل *</label>
                                    <select id="invoice-customer" name="customer_id" required>
                                        <option value="">اختر العميل</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="invoice-date">تاريخ الفاتورة *</label>
                                    <input type="date" id="invoice-date" name="invoice_date" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="invoice-due-date">تاريخ الاستحقاق</label>
                                    <input type="date" id="invoice-due-date" name="due_date">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>بنود الفاتورة</label>
                                <div id="invoice-items">
                                    <div class="invoice-item" data-item-index="0">
                                        <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr auto; gap: 10px; align-items: end; margin-bottom: 10px;">
                                            <div class="form-group" style="margin-bottom: 0;">
                                                <label>المنتج/الخدمة</label>
                                                <select name="items[0][product_id]" class="item-product" required>
                                                    <option value="">اختر المنتج</option>
                                                </select>
                                            </div>
                                            <div class="form-group" style="margin-bottom: 0;">
                                                <label>الكمية</label>
                                                <input type="number" name="items[0][quantity]" class="item-quantity" step="0.001" min="0" required>
                                            </div>
                                            <div class="form-group" style="margin-bottom: 0;">
                                                <label>سعر الوحدة</label>
                                                <input type="number" name="items[0][unit_price]" class="item-price" step="0.01" min="0" required readonly>
                                            </div>
                                            <div class="form-group" style="margin-bottom: 0;">
                                                <label>الإجمالي</label>
                                                <input type="number" class="item-total" step="0.01" readonly>
                                            </div>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="app.removeInvoiceItem(this)" style="height: 38px;">🗑️</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-secondary btn-sm" onclick="app.addInvoiceItem()">➕ إضافة بند</button>
                            </div>
                            
                            <div class="invoice-totals" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; text-align: center;">
                                    <div>
                                        <strong>المبلغ قبل الضريبة:</strong>
                                        <div id="invoice-subtotal">0.00 ريال</div>
                                    </div>
                                    <div>
                                        <strong>مبلغ الضريبة:</strong>
                                        <div id="invoice-tax">0.00 ريال</div>
                                    </div>
                                    <div>
                                        <strong>المبلغ الإجمالي:</strong>
                                        <div id="invoice-total">0.00 ريال</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">حفظ الفاتورة</button>
                                <button type="button" class="btn btn-secondary" onclick="app.hideInvoiceForm()">إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    },

    // تحميل الفواتير
    async loadInvoices() {
        try {
            const response = await this.apiRequest('/invoices');
            const invoices = await response.json();
            
            const tbody = document.getElementById('invoices-table-body');
            if (invoices.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7">لا توجد فواتير مسجلة</td></tr>';
                return;
            }

            tbody.innerHTML = invoices.map(invoice => `
                <tr>
                    <td>${invoice.invoice_number}</td>
                    <td>${invoice.customer_name || 'غير محدد'}</td>
                    <td>${new Date(invoice.invoice_date).toLocaleDateString('ar-SA')}</td>
                    <td>${invoice.grand_total} ريال</td>
                    <td>
                        <span class="badge ${this.getStatusBadgeClass(invoice.status)}">
                            ${this.getStatusLabel(invoice.status)}
                        </span>
                    </td>
                    <td>
                        <span class="badge ${this.getZatcaStatusBadgeClass(invoice.zatca_status)}">
                            ${this.getZatcaStatusLabel(invoice.zatca_status)}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-secondary" data-action="edit" data-type="invoice" data-id="${invoice.invoice_id}">
                            ✏️ تعديل
                        </button>
                        <button class="btn btn-sm btn-info" onclick="app.generateQR('${invoice.invoice_id}')">
                            📱 QR Code
                        </button>
                        <button class="btn btn-sm btn-danger" data-action="delete" data-type="invoice" data-id="${invoice.invoice_id}">
                            🗑️ حذف
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('خطأ في تحميل الفواتير:', error);
            this.showMessage('خطأ في تحميل الفواتير', 'error');
        }
    },

    // الحصول على تسمية الحالة
    getStatusLabel(status) {
        const statuses = {
            'draft': 'مسودة',
            'issued': 'صادرة',
            'paid': 'مدفوعة',
            'cancelled': 'ملغاة'
        };
        return statuses[status] || status;
    },

    // الحصول على كلاس شارة الحالة
    getStatusBadgeClass(status) {
        const classes = {
            'draft': 'badge-warning',
            'issued': 'badge-info',
            'paid': 'badge-success',
            'cancelled': 'badge-danger'
        };
        return classes[status] || 'badge-info';
    },

    // الحصول على تسمية حالة ZATCA
    getZatcaStatusLabel(status) {
        const statuses = {
            'pending': 'معلقة',
            'sent': 'مرسلة',
            'accepted': 'مقبولة',
            'rejected': 'مرفوضة'
        };
        return statuses[status] || status;
    },

    // الحصول على كلاس شارة حالة ZATCA
    getZatcaStatusBadgeClass(status) {
        const classes = {
            'pending': 'badge-warning',
            'sent': 'badge-info',
            'accepted': 'badge-success',
            'rejected': 'badge-danger'
        };
        return classes[status] || 'badge-warning';
    },

    // عرض نموذج الفاتورة
    async showInvoiceForm(invoiceId = null) {
        const modal = document.getElementById('invoice-modal');
        const title = document.getElementById('invoice-modal-title');
        const form = document.getElementById('invoice-form');
        
        // تحميل العملاء والمنتجات
        await this.loadCustomersForInvoice();
        await this.loadProductsForInvoice();
        
        if (invoiceId) {
            title.textContent = 'تعديل الفاتورة';
            this.loadInvoiceData(invoiceId);
        } else {
            title.textContent = 'فاتورة جديدة';
            form.reset();
            document.getElementById('invoice-id').value = '';
            document.getElementById('invoice-company-id').value = this.currentUser?.company_id || '';
            document.getElementById('invoice-date').value = new Date().toISOString().split('T')[0];
            this.generateInvoiceNumber();
        }
        
        modal.style.display = 'block';
        this.setupInvoiceEventListeners();
    },

    // إخفاء نموذج الفاتورة
    hideInvoiceForm() {
        document.getElementById('invoice-modal').style.display = 'none';
    },

    // تحميل العملاء للفاتورة
    async loadCustomersForInvoice() {
        try {
            const response = await this.apiRequest('/customers');
            const customers = await response.json();
            
            const select = document.getElementById('invoice-customer');
            select.innerHTML = '<option value="">اختر العميل</option>';
            
            customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.customer_id;
                option.textContent = customer.name;
                select.appendChild(option);
            });
            
        } catch (error) {
            console.error('خطأ في تحميل العملاء:', error);
        }
    },

    // تحميل المنتجات للفاتورة
    async loadProductsForInvoice() {
        try {
            const response = await this.apiRequest('/products');
            const products = await response.json();
            
            this.invoiceProducts = products.filter(p => p.is_active);
            this.updateProductSelects();
            
        } catch (error) {
            console.error('خطأ في تحميل المنتجات:', error);
        }
    },

    // تحديث قوائم المنتجات
    updateProductSelects() {
        const selects = document.querySelectorAll('.item-product');
        selects.forEach(select => {
            const currentValue = select.value;
            select.innerHTML = '<option value="">اختر المنتج</option>';
            
            this.invoiceProducts.forEach(product => {
                const option = document.createElement('option');
                option.value = product.product_id;
                option.textContent = `${product.name} - ${product.unit_price} ريال`;
                option.dataset.price = product.unit_price;
                option.dataset.taxRate = product.tax_rate;
                select.appendChild(option);
            });
            
            select.value = currentValue;
        });
    },

    // إعداد مستمعي أحداث الفاتورة
    setupInvoiceEventListeners() {
        const form = document.getElementById('invoice-form');
        
        // مستمع تغيير المنتج
        form.addEventListener('change', (e) => {
            if (e.target.classList.contains('item-product')) {
                this.updateItemPrice(e.target);
            }
            if (e.target.classList.contains('item-quantity') || e.target.classList.contains('item-price')) {
                this.calculateItemTotal(e.target.closest('.invoice-item'));
            }
        });
        
        form.addEventListener('input', (e) => {
            if (e.target.classList.contains('item-quantity') || e.target.classList.contains('item-price')) {
                this.calculateItemTotal(e.target.closest('.invoice-item'));
            }
        });
    },

    // تحديث سعر البند
    updateItemPrice(productSelect) {
        const selectedOption = productSelect.selectedOptions[0];
        const priceInput = productSelect.closest('.invoice-item').querySelector('.item-price');
        
        if (selectedOption && selectedOption.dataset.price) {
            priceInput.value = selectedOption.dataset.price;
            this.calculateItemTotal(productSelect.closest('.invoice-item'));
        } else {
            priceInput.value = '';
        }
    },

    // حساب إجمالي البند
    calculateItemTotal(itemElement) {
        const quantity = parseFloat(itemElement.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(itemElement.querySelector('.item-price').value) || 0;
        const total = quantity * price;
        
        itemElement.querySelector('.item-total').value = total.toFixed(2);
        this.calculateInvoiceTotal();
    },

    // حساب إجمالي الفاتورة
    calculateInvoiceTotal() {
        let subtotal = 0;
        let totalTax = 0;
        
        document.querySelectorAll('.invoice-item').forEach(item => {
            const quantity = parseFloat(item.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(item.querySelector('.item-price').value) || 0;
            const productSelect = item.querySelector('.item-product');
            const selectedOption = productSelect.selectedOptions[0];
            
            if (selectedOption && selectedOption.dataset.taxRate) {
                const lineTotal = quantity * price;
                const taxRate = parseFloat(selectedOption.dataset.taxRate) / 100;
                const lineTax = lineTotal * taxRate;
                
                subtotal += lineTotal;
                totalTax += lineTax;
            }
        });
        
        const grandTotal = subtotal + totalTax;
        
        document.getElementById('invoice-subtotal').textContent = `${subtotal.toFixed(2)} ريال`;
        document.getElementById('invoice-tax').textContent = `${totalTax.toFixed(2)} ريال`;
        document.getElementById('invoice-total').textContent = `${grandTotal.toFixed(2)} ريال`;
    },

    // إضافة بند جديد
    addInvoiceItem() {
        const container = document.getElementById('invoice-items');
        const itemCount = container.children.length;
        
        const newItem = document.createElement('div');
        newItem.className = 'invoice-item';
        newItem.dataset.itemIndex = itemCount;
        
        newItem.innerHTML = `
            <div style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr auto; gap: 10px; align-items: end; margin-bottom: 10px;">
                <div class="form-group" style="margin-bottom: 0;">
                    <label>المنتج/الخدمة</label>
                    <select name="items[${itemCount}][product_id]" class="item-product" required>
                        <option value="">اختر المنتج</option>
                    </select>
                </div>
                <div class="form-group" style="margin-bottom: 0;">
                    <label>الكمية</label>
                    <input type="number" name="items[${itemCount}][quantity]" class="item-quantity" step="0.001" min="0" required>
                </div>
                <div class="form-group" style="margin-bottom: 0;">
                    <label>سعر الوحدة</label>
                    <input type="number" name="items[${itemCount}][unit_price]" class="item-price" step="0.01" min="0" required readonly>
                </div>
                <div class="form-group" style="margin-bottom: 0;">
                    <label>الإجمالي</label>
                    <input type="number" class="item-total" step="0.01" readonly>
                </div>
                <button type="button" class="btn btn-danger btn-sm" onclick="app.removeInvoiceItem(this)" style="height: 38px;">🗑️</button>
            </div>
        `;
        
        container.appendChild(newItem);
        this.updateProductSelects();
    },

    // حذف بند
    removeInvoiceItem(button) {
        const item = button.closest('.invoice-item');
        if (document.querySelectorAll('.invoice-item').length > 1) {
            item.remove();
            this.calculateInvoiceTotal();
        } else {
            this.showMessage('يجب أن تحتوي الفاتورة على بند واحد على الأقل', 'warning');
        }
    },

    // توليد رقم فاتورة
    async generateInvoiceNumber() {
        try {
            const response = await this.apiRequest('/invoices');
            const invoices = await response.json();
            const nextNumber = invoices.length + 1;
            const invoiceNumber = `INV-${new Date().getFullYear()}-${nextNumber.toString().padStart(4, '0')}`;
            document.getElementById('invoice-number').value = invoiceNumber;
        } catch (error) {
            console.error('خطأ في توليد رقم الفاتورة:', error);
        }
    },

    // حفظ الفاتورة
    async saveInvoice(data) {
        try {
            const isEdit = data.id && data.id.trim() !== '';
            const method = isEdit ? 'PUT' : 'POST';
            const endpoint = isEdit ? `/invoices/${data.id}` : '/invoices';
            
            // تحضير بيانات الفاتورة
            const invoiceData = {
                company_id: data.company_id,
                customer_id: data.customer_id,
                invoice_number: data.invoice_number,
                invoice_date: data.invoice_date,
                due_date: data.due_date || null,
                items: []
            };
            
            // جمع بنود الفاتورة
            const items = document.querySelectorAll('.invoice-item');
            items.forEach((item, index) => {
                const productId = item.querySelector('.item-product').value;
                const quantity = parseFloat(item.querySelector('.item-quantity').value);
                const unitPrice = parseFloat(item.querySelector('.item-price').value);
                
                if (productId && quantity && unitPrice) {
                    invoiceData.items.push({
                        product_id: productId,
                        quantity: quantity,
                        unit_price: unitPrice
                    });
                }
            });
            
            if (invoiceData.items.length === 0) {
                this.showMessage('يجب إضافة بند واحد على الأقل للفاتورة', 'warning');
                return;
            }
            
            const response = await this.apiRequest(endpoint, {
                method,
                body: JSON.stringify(invoiceData)
            });

            if (response.ok) {
                this.hideInvoiceForm();
                this.loadInvoices();
                this.showMessage(isEdit ? 'تم تحديث الفاتورة بنجاح' : 'تم إنشاء الفاتورة بنجاح', 'success');
            } else {
                const error = await response.json();
                this.showMessage(error.error || 'خطأ في حفظ الفاتورة', 'error');
            }
        } catch (error) {
            console.error('خطأ في حفظ الفاتورة:', error);
            this.showMessage('خطأ في حفظ الفاتورة', 'error');
        }
    },

    // توليد QR Code
    async generateQR(invoiceId) {
        try {
            const response = await this.apiRequest('/zatca/generate-qr', {
                method: 'POST',
                body: JSON.stringify({ invoice_id: invoiceId })
            });

            if (response.ok) {
                const result = await response.json();
                this.showMessage('تم توليد QR Code بنجاح', 'success');
                // يمكن إضافة عرض QR Code هنا
            } else {
                const error = await response.json();
                this.showMessage(error.error || 'خطأ في توليد QR Code', 'error');
            }
        } catch (error) {
            console.error('خطأ في توليد QR Code:', error);
            this.showMessage('خطأ في توليد QR Code', 'error');
        }
    }
});
