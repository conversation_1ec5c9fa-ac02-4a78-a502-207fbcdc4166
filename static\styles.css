/* نظام المحاسبة الإلكتروني - الأنماط */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f5f5f5;
    color: #333;
    direction: rtl;
    text-align: right;
}

/* التخطيط الرئيسي */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* الشريط الجانبي */
.sidebar {
    width: 250px;
    background: #2c3e50;
    color: white;
    padding: 20px 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: transform 0.3s ease;
}

.sidebar.hidden {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid #34495e;
    margin-bottom: 20px;
}

.sidebar-header h2 {
    font-size: 1.2em;
    margin-bottom: 5px;
}

.sidebar-header p {
    font-size: 0.9em;
    opacity: 0.8;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-link {
    display: block;
    padding: 12px 20px;
    color: white;
    text-decoration: none;
    transition: background 0.3s ease;
    cursor: pointer;
}

.nav-link:hover,
.nav-link.active {
    background: #3498db;
}

.nav-link i {
    margin-left: 10px;
    width: 20px;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 250px;
    transition: margin-right 0.3s ease;
}

.main-content.full-width {
    margin-right: 0;
}

/* الرأس العلوي */
.top-header {
    background: white;
    padding: 15px 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: #3498db;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

/* صفحة تسجيل الدخول */
.login-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    max-width: 400px;
    width: 100%;
    text-align: center;
}

.login-card h1 {
    margin-bottom: 10px;
    color: #2c3e50;
}

.login-card p {
    margin-bottom: 30px;
    color: #7f8c8d;
    font-size: 0.9em;
}

.login-form {
    text-align: right;
}

.demo-info {
    margin-top: 30px;
    padding: 20px;
    background: #ecf0f1;
    border-radius: 8px;
    text-align: right;
}

.demo-info h3 {
    margin-bottom: 10px;
    color: #2c3e50;
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* الأزرار */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #229954;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

/* لوحة التحكم */
.dashboard {
    padding: 30px;
}

.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h1 {
    margin-bottom: 5px;
    color: #2c3e50;
}

.dashboard-header p {
    color: #7f8c8d;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-icon {
    font-size: 2.5em;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ecf0f1;
    border-radius: 50%;
}

.stat-info h3 {
    font-size: 2em;
    margin-bottom: 5px;
    color: #2c3e50;
}

.stat-info p {
    color: #7f8c8d;
    font-size: 0.9em;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.recent-activities,
.quick-actions {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.recent-activities h2,
.quick-actions h2 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.3em;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.action-buttons .btn {
    justify-content: flex-start;
    text-align: right;
}

/* الصفحات */
.page-container {
    padding: 30px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ecf0f1;
}

.page-header h1 {
    color: #2c3e50;
}

/* الجداول */
.table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 15px;
    text-align: right;
    border-bottom: 1px solid #ecf0f1;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #2c3e50;
    margin: 0;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: #7f8c8d;
}

.close:hover {
    color: #2c3e50;
}

.modal form {
    padding: 20px;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ecf0f1;
}

/* الرسائل */
.message {
    position: fixed;
    top: 20px;
    left: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

.message-success {
    background: #27ae60;
}

.message-error {
    background: #e74c3c;
}

.message-info {
    background: #3498db;
}

.message-warning {
    background: #f39c12;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* التجاوب */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .dashboard-content {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

/* تحسينات إضافية */
.loading {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #95a5a6;
}

.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.badge-success {
    background: #d5f4e6;
    color: #27ae60;
}

.badge-warning {
    background: #fef9e7;
    color: #f39c12;
}

.badge-danger {
    background: #fadbd8;
    color: #e74c3c;
}

.badge-info {
    background: #d6eaf8;
    color: #3498db;
}
