<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة الإلكتروني</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        .status {
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 1.2em;
        }
        .info {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: right;
        }
        .api-list {
            text-align: right;
            margin: 20px 0;
        }
        .api-list li {
            margin: 10px 0;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 5px;
            border-right: 4px solid #2196F3;
        }
        .btn {
            background: #2196F3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 نظام المحاسبة الإلكتروني</h1>

        <div class="status">
            ✅ الخادم يعمل بنجاح!
        </div>

        <div class="info">
            <h3>معلومات النظام:</h3>
            <p><strong>الخادم:</strong> Flask API Server</p>
            <p><strong>قاعدة البيانات:</strong> SQLite</p>
            <p><strong>المنفذ:</strong> 5000</p>
            <p><strong>الحالة:</strong> جاهز للاستخدام</p>
        </div>

        <div class="info">
            <h3>واجهات برمجة التطبيقات المتاحة:</h3>
            <ul class="api-list">
                <li><strong>المستخدمين:</strong> /api/auth/login, /api/auth/register, /api/users</li>
                <li><strong>الشركات:</strong> /api/companies</li>
                <li><strong>العملاء:</strong> /api/customers</li>
                <li><strong>المنتجات:</strong> /api/products</li>
                <li><strong>الفواتير:</strong> /api/invoices</li>
                <li><strong>ZATCA:</strong> /api/zatca/generate-qr</li>
            </ul>
        </div>

        <div>
            <a href="/api/companies" class="btn">اختبار API - الشركات</a>
            <a href="/api/products" class="btn">اختبار API - المنتجات</a>
        </div>

        <div class="info">
            <h3>للمطورين:</h3>
            <p>يمكنك استخدام أدوات مثل Postman أو curl لاختبار واجهات برمجة التطبيقات</p>
            <p>مثال: <code>GET http://localhost:5000/api/companies</code></p>
        </div>
    </div>

    <script>
        // إضافة بعض التفاعل
        console.log('🚀 نظام المحاسبة الإلكتروني جاهز!');

        // اختبار الاتصال بالخادم
        fetch('/api/companies')
            .then(response => {
                if (response.ok) {
                    console.log('✅ الاتصال بقاعدة البيانات ناجح');
                } else {
                    console.log('⚠️ تحقق من إعدادات قاعدة البيانات');
                }
            })
            .catch(error => {
                console.log('❌ خطأ في الاتصال:', error);
            });
    </script>
</body>
</html>

