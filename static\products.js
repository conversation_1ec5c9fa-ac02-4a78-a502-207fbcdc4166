// إدارة المنتجات
Object.assign(AccountingApp.prototype, {
    // صفحة إدارة المنتجات
    getProductsPage() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h1>إدارة المنتجات والخدمات</h1>
                    <button class="btn btn-primary" onclick="app.showProductForm()">
                        ➕ منتج جديد
                    </button>
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم المنتج</th>
                                <th>الوصف</th>
                                <th>سعر الوحدة</th>
                                <th>معدل الضريبة</th>
                                <th>الوحدة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="products-table-body">
                            <tr><td colspan="7">جاري التحميل...</td></tr>
                        </tbody>
                    </table>
                </div>
                
                <div id="product-modal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2 id="product-modal-title">منتج جديد</h2>
                            <span class="close" onclick="app.hideProductForm()">&times;</span>
                        </div>
                        <form data-form="product" id="product-form">
                            <input type="hidden" id="product-id" name="id">
                            <input type="hidden" id="product-company-id" name="company_id" value="${this.currentUser?.company_id || ''}">
                            
                            <div class="form-group">
                                <label for="product-name">اسم المنتج/الخدمة *</label>
                                <input type="text" id="product-name" name="name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="product-description">الوصف</label>
                                <textarea id="product-description" name="description"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="product-unit-price">سعر الوحدة (ريال) *</label>
                                <input type="number" id="product-unit-price" name="unit_price" step="0.01" min="0" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="product-tax-rate">معدل الضريبة (%) *</label>
                                <select id="product-tax-rate" name="tax_rate" required>
                                    <option value="0">0% - معفى من الضريبة</option>
                                    <option value="5">5% - ضريبة القيمة المضافة</option>
                                    <option value="15" selected>15% - ضريبة القيمة المضافة</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="product-unit">وحدة القياس</label>
                                <select id="product-unit" name="unit">
                                    <option value="piece">قطعة</option>
                                    <option value="kg">كيلوجرام</option>
                                    <option value="liter">لتر</option>
                                    <option value="meter">متر</option>
                                    <option value="hour">ساعة</option>
                                    <option value="day">يوم</option>
                                    <option value="month">شهر</option>
                                    <option value="year">سنة</option>
                                    <option value="service">خدمة</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="product-is-active">الحالة</label>
                                <select id="product-is-active" name="is_active">
                                    <option value="true">نشط</option>
                                    <option value="false">غير نشط</option>
                                </select>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">حفظ</button>
                                <button type="button" class="btn btn-secondary" onclick="app.hideProductForm()">إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    },

    // تحميل المنتجات
    async loadProducts() {
        try {
            const response = await this.apiRequest('/products');
            const products = await response.json();
            
            const tbody = document.getElementById('products-table-body');
            if (products.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7">لا توجد منتجات مسجلة</td></tr>';
                return;
            }

            tbody.innerHTML = products.map(product => `
                <tr>
                    <td>${product.name}</td>
                    <td>${product.description || '-'}</td>
                    <td>${product.unit_price} ريال</td>
                    <td>${product.tax_rate}%</td>
                    <td>${this.getUnitLabel(product.unit)}</td>
                    <td>
                        <span class="badge ${product.is_active ? 'badge-success' : 'badge-danger'}">
                            ${product.is_active ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-secondary" data-action="edit" data-type="product" data-id="${product.product_id}">
                            ✏️ تعديل
                        </button>
                        <button class="btn btn-sm btn-danger" data-action="delete" data-type="product" data-id="${product.product_id}">
                            🗑️ حذف
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('خطأ في تحميل المنتجات:', error);
            this.showMessage('خطأ في تحميل المنتجات', 'error');
        }
    },

    // الحصول على تسمية الوحدة
    getUnitLabel(unit) {
        const units = {
            'piece': 'قطعة',
            'kg': 'كيلوجرام',
            'liter': 'لتر',
            'meter': 'متر',
            'hour': 'ساعة',
            'day': 'يوم',
            'month': 'شهر',
            'year': 'سنة',
            'service': 'خدمة'
        };
        return units[unit] || unit;
    },

    // عرض نموذج المنتج
    showProductForm(productId = null) {
        const modal = document.getElementById('product-modal');
        const title = document.getElementById('product-modal-title');
        const form = document.getElementById('product-form');
        
        if (productId) {
            title.textContent = 'تعديل المنتج';
            this.loadProductData(productId);
        } else {
            title.textContent = 'منتج جديد';
            form.reset();
            document.getElementById('product-id').value = '';
            document.getElementById('product-company-id').value = this.currentUser?.company_id || '';
            document.getElementById('product-tax-rate').value = '15';
            document.getElementById('product-unit').value = 'piece';
            document.getElementById('product-is-active').value = 'true';
        }
        
        modal.style.display = 'block';
    },

    // إخفاء نموذج المنتج
    hideProductForm() {
        document.getElementById('product-modal').style.display = 'none';
    },

    // تحميل بيانات المنتج للتعديل
    async loadProductData(productId) {
        try {
            const response = await this.apiRequest(`/products/${productId}`);
            const product = await response.json();
            
            document.getElementById('product-id').value = product.product_id;
            document.getElementById('product-name').value = product.name;
            document.getElementById('product-description').value = product.description || '';
            document.getElementById('product-unit-price').value = product.unit_price;
            document.getElementById('product-tax-rate').value = product.tax_rate;
            document.getElementById('product-unit').value = product.unit;
            document.getElementById('product-is-active').value = product.is_active.toString();
            document.getElementById('product-company-id').value = product.company_id;
            
        } catch (error) {
            console.error('خطأ في تحميل بيانات المنتج:', error);
            this.showMessage('خطأ في تحميل بيانات المنتج', 'error');
        }
    },

    // حفظ المنتج
    async saveProduct(data) {
        try {
            const isEdit = data.id && data.id.trim() !== '';
            const method = isEdit ? 'PUT' : 'POST';
            const endpoint = isEdit ? `/products/${data.id}` : '/products';
            
            // تحويل البيانات
            const { id, ...productData } = data;
            productData.unit_price = parseFloat(productData.unit_price);
            productData.tax_rate = parseFloat(productData.tax_rate);
            productData.is_active = productData.is_active === 'true';
            
            const response = await this.apiRequest(endpoint, {
                method,
                body: JSON.stringify(productData)
            });

            if (response.ok) {
                this.hideProductForm();
                this.loadProducts();
                this.showMessage(isEdit ? 'تم تحديث المنتج بنجاح' : 'تم إضافة المنتج بنجاح', 'success');
            } else {
                const error = await response.json();
                this.showMessage(error.error || 'خطأ في حفظ المنتج', 'error');
            }
        } catch (error) {
            console.error('خطأ في حفظ المنتج:', error);
            this.showMessage('خطأ في حفظ المنتج', 'error');
        }
    }
});
