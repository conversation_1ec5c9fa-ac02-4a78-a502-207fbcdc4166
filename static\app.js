// نظام المحاسبة الإلكتروني - التطبيق الرئيسي
class AccountingApp {
    constructor() {
        this.currentUser = null;
        this.currentPage = 'login';
        this.apiBase = '/api';
        this.init();
    }

    init() {
        this.checkAuth();
        this.setupEventListeners();
        this.loadPage();
    }

    // التحقق من المصادقة
    checkAuth() {
        const token = localStorage.getItem('auth_token');
        if (token) {
            this.validateToken(token);
        }
    }

    async validateToken(token) {
        try {
            const response = await fetch(`${this.apiBase}/auth/me`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (response.ok) {
                this.currentUser = await response.json();
                this.currentPage = 'dashboard';
            } else {
                localStorage.removeItem('auth_token');
            }
        } catch (error) {
            console.error('خطأ في التحقق من المصادقة:', error);
            localStorage.removeItem('auth_token');
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action]')) {
                const action = e.target.dataset.action;
                const data = e.target.dataset;
                this.handleAction(action, data);
            }
        });

        document.addEventListener('submit', (e) => {
            if (e.target.matches('form[data-form]')) {
                e.preventDefault();
                const formType = e.target.dataset.form;
                this.handleForm(formType, e.target);
            }
        });
    }

    // معالجة الإجراءات
    handleAction(action, data) {
        switch (action) {
            case 'navigate':
                this.navigateTo(data.page);
                break;
            case 'logout':
                this.logout();
                break;
            case 'delete':
                this.deleteItem(data.type, data.id);
                break;
            case 'edit':
                this.editItem(data.type, data.id);
                break;
            default:
                console.log('إجراء غير معروف:', action);
        }
    }

    // معالجة النماذج
    async handleForm(formType, form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        switch (formType) {
            case 'login':
                await this.login(data);
                break;
            case 'register':
                await this.register(data);
                break;
            case 'company':
                await this.saveCompany(data);
                break;
            case 'customer':
                await this.saveCustomer(data);
                break;
            case 'product':
                await this.saveProduct(data);
                break;
            case 'invoice':
                await this.saveInvoice(data);
                break;
            case 'user':
                await this.saveUser(data);
                break;
        }
    }

    // تسجيل الدخول
    async login(data) {
        try {
            const response = await fetch(`${this.apiBase}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (response.ok) {
                localStorage.setItem('auth_token', result.token);
                this.currentUser = result.user;
                this.navigateTo('dashboard');
                this.showMessage('تم تسجيل الدخول بنجاح', 'success');
            } else {
                this.showMessage(result.error || 'خطأ في تسجيل الدخول', 'error');
            }
        } catch (error) {
            this.showMessage('خطأ في الاتصال بالخادم', 'error');
        }
    }

    // تسجيل الخروج
    logout() {
        localStorage.removeItem('auth_token');
        this.currentUser = null;
        this.navigateTo('login');
        this.showMessage('تم تسجيل الخروج بنجاح', 'info');
    }

    // التنقل بين الصفحات
    navigateTo(page) {
        this.currentPage = page;
        this.loadPage();
        this.updateNavigation();
    }

    // تحميل الصفحة
    loadPage() {
        const content = document.getElementById('app-content');
        
        switch (this.currentPage) {
            case 'login':
                content.innerHTML = this.getLoginPage();
                break;
            case 'dashboard':
                content.innerHTML = this.getDashboardPage();
                this.loadDashboardData();
                break;
            case 'companies':
                content.innerHTML = this.getCompaniesPage();
                this.loadCompanies();
                break;
            case 'customers':
                content.innerHTML = this.getCustomersPage();
                this.loadCustomers();
                break;
            case 'products':
                content.innerHTML = this.getProductsPage();
                this.loadProducts();
                break;
            case 'invoices':
                content.innerHTML = this.getInvoicesPage();
                this.loadInvoices();
                break;
            case 'users':
                content.innerHTML = this.getUsersPage();
                this.loadUsers();
                break;
            case 'reports':
                content.innerHTML = this.getReportsPage();
                this.loadReports();
                break;
            case 'settings':
                content.innerHTML = this.getSettingsPage();
                break;
            default:
                content.innerHTML = '<div class="error">صفحة غير موجودة</div>';
        }
    }

    // تحديث التنقل
    updateNavigation() {
        const nav = document.getElementById('main-nav');
        if (this.currentUser) {
            nav.style.display = 'block';
            nav.querySelectorAll('[data-action="navigate"]').forEach(link => {
                link.classList.toggle('active', link.dataset.page === this.currentPage);
            });
        } else {
            nav.style.display = 'none';
        }
    }

    // عرض الرسائل
    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }

    // طلبات API مع المصادقة
    async apiRequest(endpoint, options = {}) {
        const token = localStorage.getItem('auth_token');
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };

        if (token) {
            headers.Authorization = `Bearer ${token}`;
        }

        const response = await fetch(`${this.apiBase}${endpoint}`, {
            ...options,
            headers
        });

        if (response.status === 401) {
            this.logout();
            throw new Error('انتهت صلاحية الجلسة');
        }

        return response;
    }

    // صفحة تسجيل الدخول
    getLoginPage() {
        return `
            <div class="login-container">
                <div class="login-card">
                    <h1>🏢 نظام المحاسبة الإلكتروني</h1>
                    <p>متوافق مع متطلبات هيئة الزكاة والدخل السعودية</p>
                    
                    <form data-form="login" class="login-form">
                        <div class="form-group">
                            <label for="username">اسم المستخدم</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">كلمة المرور</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
                    </form>
                    
                    <div class="demo-info">
                        <h3>للتجربة:</h3>
                        <p>اسم المستخدم: admin</p>
                        <p>كلمة المرور: admin123</p>
                    </div>
                </div>
            </div>
        `;
    }

    // لوحة التحكم الرئيسية
    getDashboardPage() {
        return `
            <div class="dashboard">
                <div class="dashboard-header">
                    <h1>لوحة التحكم الرئيسية</h1>
                    <p>مرحباً ${this.currentUser?.username || 'المستخدم'}</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">🏢</div>
                        <div class="stat-info">
                            <h3 id="companies-count">0</h3>
                            <p>الشركات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-info">
                            <h3 id="customers-count">0</h3>
                            <p>العملاء</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">📦</div>
                        <div class="stat-info">
                            <h3 id="products-count">0</h3>
                            <p>المنتجات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">🧾</div>
                        <div class="stat-info">
                            <h3 id="invoices-count">0</h3>
                            <p>الفواتير</p>
                        </div>
                    </div>
                </div>
                
                <div class="dashboard-content">
                    <div class="recent-activities">
                        <h2>الأنشطة الحديثة</h2>
                        <div id="recent-activities-list">
                            <p>لا توجد أنشطة حديثة</p>
                        </div>
                    </div>
                    
                    <div class="quick-actions">
                        <h2>إجراءات سريعة</h2>
                        <div class="action-buttons">
                            <button class="btn btn-primary" data-action="navigate" data-page="invoices">
                                🧾 فاتورة جديدة
                            </button>
                            <button class="btn btn-secondary" data-action="navigate" data-page="customers">
                                👥 عميل جديد
                            </button>
                            <button class="btn btn-secondary" data-action="navigate" data-page="products">
                                📦 منتج جديد
                            </button>
                            <button class="btn btn-secondary" data-action="navigate" data-page="reports">
                                📊 التقارير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // صفحة إدارة الشركات
    getCompaniesPage() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h1>إدارة الشركات</h1>
                    <button class="btn btn-primary" onclick="app.showCompanyForm()">
                        ➕ شركة جديدة
                    </button>
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم الشركة</th>
                                <th>الرقم الضريبي</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="companies-table-body">
                            <tr><td colspan="5">جاري التحميل...</td></tr>
                        </tbody>
                    </table>
                </div>
                
                <div id="company-modal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2 id="company-modal-title">شركة جديدة</h2>
                            <span class="close" onclick="app.hideCompanyForm()">&times;</span>
                        </div>
                        <form data-form="company" id="company-form">
                            <input type="hidden" id="company-id" name="id">
                            
                            <div class="form-group">
                                <label for="company-name">اسم الشركة *</label>
                                <input type="text" id="company-name" name="name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="company-tax-id">الرقم الضريبي *</label>
                                <input type="text" id="company-tax-id" name="tax_id" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="company-address">العنوان</label>
                                <textarea id="company-address" name="address"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="company-contact">شخص الاتصال</label>
                                <input type="text" id="company-contact" name="contact_person">
                            </div>
                            
                            <div class="form-group">
                                <label for="company-email">البريد الإلكتروني</label>
                                <input type="email" id="company-email" name="email">
                            </div>
                            
                            <div class="form-group">
                                <label for="company-phone">رقم الهاتف</label>
                                <input type="tel" id="company-phone" name="phone">
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">حفظ</button>
                                <button type="button" class="btn btn-secondary" onclick="app.hideCompanyForm()">إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    }

    // تحميل بيانات لوحة التحكم
    async loadDashboardData() {
        try {
            const [companies, customers, products, invoices] = await Promise.all([
                this.apiRequest('/companies'),
                this.apiRequest('/customers'),
                this.apiRequest('/products'),
                this.apiRequest('/invoices')
            ]);

            const companiesData = await companies.json();
            const customersData = await customers.json();
            const productsData = await products.json();
            const invoicesData = await invoices.json();

            document.getElementById('companies-count').textContent = companiesData.length || 0;
            document.getElementById('customers-count').textContent = customersData.length || 0;
            document.getElementById('products-count').textContent = productsData.length || 0;
            document.getElementById('invoices-count').textContent = invoicesData.length || 0;

        } catch (error) {
            console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
        }
    }

    // تحميل الشركات
    async loadCompanies() {
        try {
            const response = await this.apiRequest('/companies');
            const companies = await response.json();
            
            const tbody = document.getElementById('companies-table-body');
            if (companies.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5">لا توجد شركات مسجلة</td></tr>';
                return;
            }

            tbody.innerHTML = companies.map(company => `
                <tr>
                    <td>${company.name}</td>
                    <td>${company.tax_id}</td>
                    <td>${company.email || '-'}</td>
                    <td>${company.phone || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-secondary" data-action="edit" data-type="company" data-id="${company.company_id}">
                            ✏️ تعديل
                        </button>
                        <button class="btn btn-sm btn-danger" data-action="delete" data-type="company" data-id="${company.company_id}">
                            🗑️ حذف
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('خطأ في تحميل الشركات:', error);
            this.showMessage('خطأ في تحميل الشركات', 'error');
        }
    }

    // عرض نموذج الشركة
    showCompanyForm(companyId = null) {
        const modal = document.getElementById('company-modal');
        const title = document.getElementById('company-modal-title');
        const form = document.getElementById('company-form');
        
        if (companyId) {
            title.textContent = 'تعديل الشركة';
            this.loadCompanyData(companyId);
        } else {
            title.textContent = 'شركة جديدة';
            form.reset();
            document.getElementById('company-id').value = '';
        }
        
        modal.style.display = 'block';
    }

    // إخفاء نموذج الشركة
    hideCompanyForm() {
        document.getElementById('company-modal').style.display = 'none';
    }

    // حفظ الشركة
    async saveCompany(data) {
        try {
            const isEdit = data.id && data.id.trim() !== '';
            const method = isEdit ? 'PUT' : 'POST';
            const endpoint = isEdit ? `/companies/${data.id}` : '/companies';
            
            // إزالة الـ id من البيانات المرسلة
            const { id, ...companyData } = data;
            
            const response = await this.apiRequest(endpoint, {
                method,
                body: JSON.stringify(companyData)
            });

            if (response.ok) {
                this.hideCompanyForm();
                this.loadCompanies();
                this.showMessage(isEdit ? 'تم تحديث الشركة بنجاح' : 'تم إضافة الشركة بنجاح', 'success');
            } else {
                const error = await response.json();
                this.showMessage(error.error || 'خطأ في حفظ الشركة', 'error');
            }
        } catch (error) {
            console.error('خطأ في حفظ الشركة:', error);
            this.showMessage('خطأ في حفظ الشركة', 'error');
        }
    }
}

// تشغيل التطبيق
const app = new AccountingApp();
