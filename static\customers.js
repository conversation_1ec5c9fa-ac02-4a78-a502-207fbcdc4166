// إدارة العملاء
Object.assign(AccountingApp.prototype, {
    // صفحة إدارة العملاء
    getCustomersPage() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h1>إدارة العملاء</h1>
                    <button class="btn btn-primary" onclick="app.showCustomerForm()">
                        ➕ عميل جديد
                    </button>
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>الرقم الضريبي</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>العنوان</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customers-table-body">
                            <tr><td colspan="6">جاري التحميل...</td></tr>
                        </tbody>
                    </table>
                </div>
                
                <div id="customer-modal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2 id="customer-modal-title">عميل جديد</h2>
                            <span class="close" onclick="app.hideCustomerForm()">&times;</span>
                        </div>
                        <form data-form="customer" id="customer-form">
                            <input type="hidden" id="customer-id" name="id">
                            <input type="hidden" id="customer-company-id" name="company_id" value="${this.currentUser?.company_id || ''}">
                            
                            <div class="form-group">
                                <label for="customer-name">اسم العميل *</label>
                                <input type="text" id="customer-name" name="name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="customer-tax-id">الرقم الضريبي</label>
                                <input type="text" id="customer-tax-id" name="tax_id">
                            </div>
                            
                            <div class="form-group">
                                <label for="customer-email">البريد الإلكتروني</label>
                                <input type="email" id="customer-email" name="email">
                            </div>
                            
                            <div class="form-group">
                                <label for="customer-phone">رقم الهاتف</label>
                                <input type="tel" id="customer-phone" name="phone">
                            </div>
                            
                            <div class="form-group">
                                <label for="customer-address">العنوان</label>
                                <textarea id="customer-address" name="address"></textarea>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">حفظ</button>
                                <button type="button" class="btn btn-secondary" onclick="app.hideCustomerForm()">إلغاء</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;
    },

    // تحميل العملاء
    async loadCustomers() {
        try {
            const response = await this.apiRequest('/customers');
            const customers = await response.json();
            
            const tbody = document.getElementById('customers-table-body');
            if (customers.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6">لا يوجد عملاء مسجلين</td></tr>';
                return;
            }

            tbody.innerHTML = customers.map(customer => `
                <tr>
                    <td>${customer.name}</td>
                    <td>${customer.tax_id || '-'}</td>
                    <td>${customer.email || '-'}</td>
                    <td>${customer.phone || '-'}</td>
                    <td>${customer.address || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-secondary" data-action="edit" data-type="customer" data-id="${customer.customer_id}">
                            ✏️ تعديل
                        </button>
                        <button class="btn btn-sm btn-danger" data-action="delete" data-type="customer" data-id="${customer.customer_id}">
                            🗑️ حذف
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('خطأ في تحميل العملاء:', error);
            this.showMessage('خطأ في تحميل العملاء', 'error');
        }
    },

    // عرض نموذج العميل
    showCustomerForm(customerId = null) {
        const modal = document.getElementById('customer-modal');
        const title = document.getElementById('customer-modal-title');
        const form = document.getElementById('customer-form');
        
        if (customerId) {
            title.textContent = 'تعديل العميل';
            this.loadCustomerData(customerId);
        } else {
            title.textContent = 'عميل جديد';
            form.reset();
            document.getElementById('customer-id').value = '';
            document.getElementById('customer-company-id').value = this.currentUser?.company_id || '';
        }
        
        modal.style.display = 'block';
    },

    // إخفاء نموذج العميل
    hideCustomerForm() {
        document.getElementById('customer-modal').style.display = 'none';
    },

    // تحميل بيانات العميل للتعديل
    async loadCustomerData(customerId) {
        try {
            const response = await this.apiRequest(`/customers/${customerId}`);
            const customer = await response.json();
            
            document.getElementById('customer-id').value = customer.customer_id;
            document.getElementById('customer-name').value = customer.name;
            document.getElementById('customer-tax-id').value = customer.tax_id || '';
            document.getElementById('customer-email').value = customer.email || '';
            document.getElementById('customer-phone').value = customer.phone || '';
            document.getElementById('customer-address').value = customer.address || '';
            document.getElementById('customer-company-id').value = customer.company_id;
            
        } catch (error) {
            console.error('خطأ في تحميل بيانات العميل:', error);
            this.showMessage('خطأ في تحميل بيانات العميل', 'error');
        }
    },

    // حفظ العميل
    async saveCustomer(data) {
        try {
            const isEdit = data.id && data.id.trim() !== '';
            const method = isEdit ? 'PUT' : 'POST';
            const endpoint = isEdit ? `/customers/${data.id}` : '/customers';
            
            // إزالة الـ id من البيانات المرسلة
            const { id, ...customerData } = data;
            
            const response = await this.apiRequest(endpoint, {
                method,
                body: JSON.stringify(customerData)
            });

            if (response.ok) {
                this.hideCustomerForm();
                this.loadCustomers();
                this.showMessage(isEdit ? 'تم تحديث العميل بنجاح' : 'تم إضافة العميل بنجاح', 'success');
            } else {
                const error = await response.json();
                this.showMessage(error.error || 'خطأ في حفظ العميل', 'error');
            }
        } catch (error) {
            console.error('خطأ في حفظ العميل:', error);
            this.showMessage('خطأ في حفظ العميل', 'error');
        }
    }
});
